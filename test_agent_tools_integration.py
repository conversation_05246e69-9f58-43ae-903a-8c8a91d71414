"""
Agent工具调用能力综合测试

测试Agent的高德API调用能力和数据库记忆功能，使用用户ID=1进行测试。
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config import get_settings
from src.core.llm_manager import LLMManager
from tools.Amap.map_tool import MapTool
from src.database.mysql_client import get_db
from src.models.mysql_crud import user_memory_crud, user_summary_crud, itinerary_crud
from src.models.user.user_schemas import UserMemory, UserSummary


async def test_amap_tools():
    """测试高德地图API工具"""
    print("=" * 60)
    print("测试1: 高德地图API工具")
    print("=" * 60)
    
    try:
        # 初始化MapTool
        map_tool = MapTool()
        
        # 测试1: 地理编码
        print("1.1 测试地理编码...")
        geo_result = map_tool.geocode_address("北京市天安门广场")
        print(f"✓ 地理编码成功")
        print(f"  - 地址: 北京市天安门广场")
        print(f"  - 结果: {json.dumps(geo_result, ensure_ascii=False, indent=2)[:200]}...")
        
        # 测试2: 天气查询
        print("\n1.2 测试天气查询...")
        weather_result = map_tool.get_weather_info("110000")  # 北京市adcode
        print(f"✓ 天气查询成功")
        print(f"  - 城市: 北京")
        print(f"  - 结果: {json.dumps(weather_result, ensure_ascii=False, indent=2)[:200]}...")
        
        # 测试3: POI搜索
        print("\n1.3 测试POI搜索...")
        poi_results = map_tool.search_pois(
            keywords="景点",
            city="北京",
            page_size=5
        )
        print(poi_results)
        print(f"✓ POI搜索成功")
        print(f"  - 关键词: 景点")
        print(f"  - 城市: 北京")
        print(f"  - 找到: {len(poi_results)} 个结果")
        for i, poi in enumerate(poi_results[:3]):
            print(f"    {i+1}. {poi.name} - {poi.type}")
        
        # 测试4: 路线规划
        print("\n1.4 测试路线规划...")
        try:
            from tools.Amap.map_tool import Location
            origin_location = Location(longitude=116.397470, latitude=39.908823, name="天安门广场")
            dest_location = Location(longitude=116.397128, latitude=39.918058, name="故宫博物院")

            route_result = map_tool.get_route(
                origin=origin_location,
                destination=dest_location,
                transport_mode="walking"
            )
            print(f"✓ 路线规划成功")
            print(f"  - 起点: 天安门广场")
            print(f"  - 终点: 故宫博物院")
            print(f"  - 交通方式: 步行")
            print(f"  - 结果: {json.dumps(route_result, ensure_ascii=False, indent=2)[:200]}...")
        except Exception as e:
            print(f"⚠️ 路线规划失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 高德地图API测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def test_database_memory():
    """测试数据库记忆功能"""
    print("\n" + "=" * 60)
    print("测试2: 数据库记忆功能")
    print("=" * 60)
    
    user_id = 1  # 使用用户ID=1进行测试
    
    try:
        async with get_db() as db:
            # 测试1: 创建用户记忆
            print("2.1 测试创建用户记忆...")
            memory_data = {
                "user_id": user_id,
                "memory_content": "用户喜欢历史文化景点，特别是故宫和长城",
                "source_session_id": f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "confidence": 0.95
            }
            
            created_memory = await user_memory_crud.create_memory(db, memory_data=memory_data)
            print(f"✓ 创建用户记忆成功")
            print(f"  - 用户ID: {created_memory.user_id}")
            print(f"  - 内容: {created_memory.memory_content}")
            print(f"  - 置信度: {created_memory.confidence}")
            print(f"  - 记忆ID: {created_memory.id}")
            
            # 测试2: 查询用户记忆
            print("\n2.2 测试查询用户记忆...")
            user_memories = await user_memory_crud.get_by_user(db, user_id=user_id, limit=10)
            print(f"✓ 查询用户记忆成功")
            print(f"  - 用户ID: {user_id}")
            print(f"  - 记忆数量: {len(user_memories)}")
            
            for i, memory in enumerate(user_memories[:3]):
                print(f"    {i+1}. {memory.memory_content[:50]}... (置信度: {memory.confidence})")
            
            # 测试3: 搜索记忆
            print("\n2.3 测试搜索记忆...")
            search_memories = await user_memory_crud.search_memories(
                db, 
                user_id=user_id, 
                keyword="历史", 
                min_confidence=0.8
            )
            print(f"✓ 搜索记忆成功")
            print(f"  - 关键词: 历史")
            print(f"  - 最小置信度: 0.8")
            print(f"  - 找到: {len(search_memories)} 条记忆")
            
            # 测试4: 获取高置信度记忆
            print("\n2.4 测试获取高置信度记忆...")
            high_conf_memories = await user_memory_crud.get_high_confidence_memories(
                db, 
                user_id=user_id, 
                min_confidence=0.9, 
                limit=5
            )
            print(f"✓ 获取高置信度记忆成功")
            print(f"  - 最小置信度: 0.9")
            print(f"  - 找到: {len(high_conf_memories)} 条记忆")
            
            # 测试5: 查询用户画像
            print("\n2.5 测试查询用户画像...")
            user_summary = await user_summary_crud.get_by_user(db, user_id=user_id)
            if user_summary:
                print(f"✓ 查询用户画像成功")
                print(f"  - 用户ID: {user_summary.user_id}")
                print(f"  - 画像摘要: {user_summary.summary}")
                print(f"  - 关键词: {user_summary.keywords}")
            else:
                print(f"⚠️ 用户画像不存在，创建默认画像...")
                summary_data = {
                    "user_id": user_id,
                    "summary": "喜欢历史文化的旅行者",
                    "keywords": ["历史", "文化", "景点"],
                    "travel_style": "深度游",
                    "budget_preference": "中等"
                }
                created_summary = await user_summary_crud.create_summary(db, summary_data=summary_data)
                print(f"✓ 创建用户画像成功")
                print(f"  - 用户ID: {created_summary.user_id}")
                print(f"  - 画像摘要: {created_summary.summary}")
            
            # 测试6: 查询历史行程
            print("\n2.6 测试查询历史行程...")
            itineraries = await itinerary_crud.get_by_user(db, user_id=user_id, limit=5)
            print(f"✓ 查询历史行程成功")
            print(f"  - 用户ID: {user_id}")
            print(f"  - 行程数量: {len(itineraries)}")
            
            for i, itinerary in enumerate(itineraries[:3]):
                print(f"    {i+1}. {itinerary.title} - {itinerary.city_name} ({itinerary.total_days}天)")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库记忆测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def test_agent_llm_integration():
    """测试Agent与LLM的集成"""
    print("\n" + "=" * 60)
    print("测试3: Agent与LLM集成")
    print("=" * 60)
    
    try:
        llm_manager = LLMManager()
        
        # 测试1: 基于用户记忆的个性化推荐
        print("3.1 测试基于用户记忆的个性化推荐...")
        
        # 模拟从数据库获取的用户记忆
        user_memories = [
            "用户喜欢历史文化景点，特别是故宫和长城",
            "用户偏好深度游，不喜欢走马观花",
            "用户预算中等，大约3000-5000元",
            "用户对美食很感兴趣，特别是地方特色小吃"
        ]
        
        # 构建个性化推荐prompt
        recommendation_prompt = f"""
        基于用户的历史记忆，为用户推荐北京3天旅行方案：
        
        用户记忆：
        {chr(10).join(f"- {memory}" for memory in user_memories)}
        
        请生成个性化的旅行推荐，包括：
        1. 推荐景点（符合用户偏好）
        2. 美食推荐
        3. 预算估算
        4. 行程安排建议
        
        请用JSON格式回复。
        """
        
        response = await llm_manager.chat(
            message=recommendation_prompt,
            role="basic"
        )
        
        print(f"✓ 个性化推荐成功")
        print(f"  - 基于记忆数量: {len(user_memories)}")
        print(f"  - LLM响应长度: {len(response.get('content', ''))} 字符")
        print(f"  - 推荐内容预览:")
        print(f"    {response.get('content', '')[:300]}...")
        
        # 测试2: 结合高德API数据的智能分析
        print("\n3.2 测试结合高德API数据的智能分析...")
        
        # 模拟高德API返回的POI数据
        poi_data = [
            {"name": "故宫博物院", "category": "景点", "rating": 4.8, "address": "北京市东城区景山前街4号"},
            {"name": "天安门广场", "category": "景点", "rating": 4.7, "address": "北京市东城区东长安街"},
            {"name": "全聚德烤鸭店", "category": "美食", "rating": 4.5, "address": "北京市东城区前门大街30号"}
        ]
        
        analysis_prompt = f"""
        基于高德地图API返回的POI数据，分析这些地点的特点：
        
        POI数据：
        {json.dumps(poi_data, ensure_ascii=False, indent=2)}
        
        请分析：
        1. 各个POI的特色和亮点
        2. 适合的游览时间和顺序
        3. 交通便利性
        4. 推荐理由
        
        请简洁回复。
        """
        
        analysis_response = await llm_manager.chat(
            message=analysis_prompt,
            role="basic"
        )
        
        print(f"✓ POI数据分析成功")
        print(f"  - 分析POI数量: {len(poi_data)}")
        print(f"  - 分析结果:")
        print(f"    {analysis_response.get('content', '')[:400]}...")
        
        await llm_manager.close_all()
        return True
        
    except Exception as e:
        print(f"✗ Agent与LLM集成测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def test_complete_workflow():
    """测试完整的工作流程"""
    print("\n" + "=" * 60)
    print("测试4: 完整工作流程模拟")
    print("=" * 60)
    
    user_id = 1
    query = "我想去北京玩3天，主要想看历史文化景点"
    
    try:
        print(f"模拟用户查询: {query}")
        print(f"用户ID: {user_id}")
        
        # 步骤1: 获取用户记忆
        print("\n4.1 获取用户记忆...")
        async with get_db() as db:
            user_memories = await user_memory_crud.get_by_user(db, user_id=user_id, limit=5)
            user_summary = await user_summary_crud.get_by_user(db, user_id=user_id)
            
        print(f"✓ 获取到 {len(user_memories)} 条用户记忆")
        if user_summary:
            print(f"✓ 获取到用户画像: {user_summary.summary}")
        
        # 步骤2: 调用高德API获取POI
        print("\n4.2 调用高德API获取POI...")
        map_tool = MapTool()
        poi_results = map_tool.search_pois(
            keywords="历史文化景点",
            city="北京",
            page_size=5
        )
        print(f"✓ 获取到 {len(poi_results)} 个POI")
        
        # 步骤3: LLM综合分析
        print("\n4.3 LLM综合分析...")
        llm_manager = LLMManager()
        
        # 构建综合分析prompt
        memories_text = "\n".join([f"- {memory.memory_content}" for memory in user_memories[:3]])
        pois_text = "\n".join([f"- {poi.name}: {poi.type}" for poi in poi_results[:3]])
        
        comprehensive_prompt = f"""
        用户查询: {query}
        
        用户历史记忆:
        {memories_text}
        
        高德API返回的相关POI:
        {pois_text}
        
        请综合分析并生成个性化的旅行建议，包括：
        1. 推荐的具体景点
        2. 行程安排
        3. 个性化建议
        
        请简洁回复。
        """
        
        response = await llm_manager.chat(
            message=comprehensive_prompt,
            role="basic"
        )
        
        print(f"✓ LLM综合分析完成")
        print(f"  - 分析结果:")
        print(f"    {response.get('content', '')[:500]}...")
        
        # 步骤4: 保存新的用户记忆
        print("\n4.4 保存新的用户记忆...")
        async with get_db() as db:
            new_memory_data = {
                "user_id": user_id,
                "memory_content": f"用户查询了北京3天历史文化景点旅行，系统推荐了{len(poi_results)}个相关POI",
                "source_session_id": f"workflow_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "confidence": 0.9
            }
            
            new_memory = await user_memory_crud.create_memory(db, memory_data=new_memory_data)
            print(f"✓ 保存新记忆成功，记忆ID: {new_memory.id}")
        
        await llm_manager.close_all()
        
        print("\n✓ 完整工作流程测试成功")
        print("  - 成功获取用户记忆")
        print("  - 成功调用高德API")
        print("  - 成功进行LLM分析")
        print("  - 成功保存新记忆")
        
        return True
        
    except Exception as e:
        print(f"✗ 完整工作流程测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def main():
    """主测试函数"""
    print("AutoPilot AI - Agent工具调用能力综合测试")
    print("=" * 60)
    print("本测试将验证:")
    print("1. 高德地图API工具调用")
    print("2. 数据库记忆功能")
    print("3. Agent与LLM集成")
    print("4. 完整工作流程")
    print("=" * 60)
    print(f"测试用户ID: 1")
    print("=" * 60)
    
    results = []
    
    try:
        # 运行所有测试
        results.append(await test_amap_tools())
        results.append(await test_database_memory())
        results.append(await test_agent_llm_integration())
        results.append(await test_complete_workflow())
        
        # 总结结果
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        test_names = [
            "高德地图API工具",
            "数据库记忆功能",
            "Agent与LLM集成",
            "完整工作流程"
        ]
        
        passed = sum(results)
        total = len(results)
        
        for i, (name, result) in enumerate(zip(test_names, results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{i+1}. {name}: {status}")
        
        print(f"\n总体结果: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有工具调用功能正常! Agent可以完整地进行旅行规划。")
            print("💡 高德API、数据库记忆、LLM分析都能正常协作。")
        else:
            print("⚠️  部分功能存在问题，需要进一步调试。")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
