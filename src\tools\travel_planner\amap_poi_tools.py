"""
高德地图POI搜索工具

集成高德地图API，提供真实的POI搜索功能
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class AmapPOISearchTool:
    """高德地图POI搜索工具"""
    
    def __init__(self, api_key: str):
        """
        初始化高德地图POI搜索工具
        
        Args:
            api_key: 高德地图API密钥
        """
        self.api_key = api_key
        self.base_url = "https://restapi.amap.com/v3"
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def search_poi(
        self,
        city: str,
        keywords: str,
        poi_type: Optional[str] = None,
        page_size: int = 20,
        page_num: int = 1
    ) -> List[Dict[str, Any]]:
        """
        搜索POI
        
        Args:
            city: 城市名称
            keywords: 搜索关键词
            poi_type: POI类型（可选）
            page_size: 每页数量
            page_num: 页码
            
        Returns:
            POI列表
        """
        try:
            logger.info(f"搜索POI: 城市={city}, 关键词={keywords}, 类型={poi_type}")
            
            session = await self._get_session()
            
            # 构建请求参数
            params = {
                "key": self.api_key,
                "keywords": keywords,
                "city": city,
                "output": "json",
                "offset": page_size,
                "page": page_num,
                "extensions": "all"
            }
            
            if poi_type:
                params["types"] = poi_type
            
            # 发送请求
            url = f"{self.base_url}/place/text"
            async with session.get(url, params=params) as response:
                if response.status != 200:
                    logger.error(f"高德地图API请求失败: {response.status}")
                    return []
                
                data = await response.json()
                
                if data.get("status") != "1":
                    logger.error(f"高德地图API返回错误: {data.get('info')}")
                    return []
                
                # 解析POI数据
                pois = []
                for poi_data in data.get("pois", []):
                    poi = self._parse_poi_data(poi_data)
                    if poi:
                        pois.append(poi)
                
                logger.info(f"成功获取 {len(pois)} 个POI")
                return pois
                
        except Exception as e:
            logger.error(f"POI搜索失败: {str(e)}")
            return []
    
    def _parse_poi_data(self, poi_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析POI数据"""
        try:
            location = poi_data.get("location", "").split(",")
            if len(location) != 2:
                return None
            
            longitude = float(location[0])
            latitude = float(location[1])
            
            # 确定POI类型
            poi_type = self._determine_poi_type(poi_data.get("type", ""))
            
            return {
                "poi_id": poi_data.get("id"),
                "name": poi_data.get("name"),
                "poi_type": poi_type,
                "address": poi_data.get("address", ""),
                "location": {
                    "longitude": longitude,
                    "latitude": latitude
                },
                "rating": self._extract_rating(poi_data),
                "description": poi_data.get("business_area", ""),
                "phone": poi_data.get("tel", ""),
                "images": [],  # 高德API不直接提供图片
                "suggested_time": self._suggest_visit_time(poi_type, poi_data.get("name", "")),
                "price_level": self._estimate_price_level(poi_data),
                "tags": self._extract_tags(poi_data)
            }
            
        except Exception as e:
            logger.error(f"解析POI数据失败: {str(e)}")
            return None
    
    def _determine_poi_type(self, amap_type: str) -> str:
        """根据高德地图类型确定POI类型"""
        type_mapping = {
            "风景名胜": "ATTRACTION",
            "公园广场": "ATTRACTION", 
            "博物馆": "ATTRACTION",
            "文物古迹": "ATTRACTION",
            "宗教场所": "ATTRACTION",
            "中餐厅": "RESTAURANT",
            "外国餐厅": "RESTAURANT",
            "快餐厅": "RESTAURANT",
            "咖啡厅": "RESTAURANT",
            "酒店": "HOTEL",
            "宾馆": "HOTEL",
            "民宿": "HOTEL",
            "购物中心": "SHOPPING",
            "商场": "SHOPPING",
            "超市": "SHOPPING"
        }
        
        for key, value in type_mapping.items():
            if key in amap_type:
                return value
        
        return "OTHER"
    
    def _extract_rating(self, poi_data: Dict[str, Any]) -> float:
        """提取评分"""
        # 高德地图API可能不直接提供评分，返回默认值
        return 4.0
    
    def _suggest_visit_time(self, poi_type: str, poi_name: str = "") -> int:
        """建议游览时间（分钟）- 根据POI类型和名称智能推荐"""

        # 特殊景点的建议游玩时间（基于实际经验）
        special_attractions = {
            "故宫博物院": 240,      # 4小时
            "故宫": 240,
            "紫禁城": 240,
            "天坛公园": 180,        # 3小时
            "天坛": 180,
            "颐和园": 240,          # 4小时
            "圆明园": 180,          # 3小时
            "长城": 300,            # 5小时
            "八达岭长城": 300,
            "慕田峪长城": 300,
            "上海博物馆": 180,      # 3小时
            "中国国家博物馆": 180,
            "南京博物院": 180,
            "西湖": 240,            # 4小时
            "外滩": 120,            # 2小时
            "陆家嘴": 120,
            "东方明珠": 120,
            "广州塔": 120,
            "黄鹤楼": 90,           # 1.5小时
            "岳阳楼": 90,
            "滕王阁": 90,
        }

        # 检查是否为特殊景点
        for attraction_name, duration in special_attractions.items():
            if attraction_name in poi_name:
                return duration

        # 默认时间映射
        time_mapping = {
            "ATTRACTION": 150,  # 一般景点2.5小时（增加了30分钟）
            "RESTAURANT": 90,   # 餐厅1.5小时
            "HOTEL": 0,         # 酒店不计算游览时间
            "SHOPPING": 90,     # 购物1.5小时（增加了30分钟）
            "OTHER": 60
        }
        return time_mapping.get(poi_type, 60)
    
    def _estimate_price_level(self, poi_data: Dict[str, Any]) -> str:
        """估算价格等级"""
        # 基于POI类型和区域估算
        return "中等"
    
    def _extract_tags(self, poi_data: Dict[str, Any]) -> List[str]:
        """提取标签"""
        tags = []
        
        # 从类型中提取标签
        poi_type = poi_data.get("type", "")
        if poi_type:
            tags.append(poi_type.split(";")[0])  # 取第一级分类
        
        # 从商圈信息中提取标签
        business_area = poi_data.get("business_area", "")
        if business_area:
            tags.append(business_area)
        
        return tags
    
    async def close(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()


# 工具函数，供统一注册表使用
async def search_poi(
    keywords: str,
    city: str,
    types: Optional[str] = None,
    poi_type: Optional[str] = None,
    page_size: Optional[int] = None,
    limit: Optional[int] = None,
    **kwargs
) -> List[Dict[str, Any]]:
    """
    搜索POI的统一接口 - 兼容ICP规划节点的参数格式
    实现3次重试逻辑，确保搜索的可靠性

    Args:
        keywords: 搜索关键词
        city: 城市名称
        types: POI类型（ICP规划节点使用的参数名）
        poi_type: POI类型（备用参数名）
        page_size: 每页数量（ICP规划节点使用的参数名）
        limit: 返回数量限制（备用参数名）
        **kwargs: 其他可能的参数

    Returns:
        POI列表
    """
    import asyncio

    logger.info(f"search_poi调用参数: keywords={keywords}, city={city}, types={types}, poi_type={poi_type}, page_size={page_size}, limit={limit}")

    # 参数兼容性处理
    final_poi_type = types or poi_type
    final_limit = page_size or limit or 10

    # 获取API密钥
    try:
        from src.core.config import get_settings
        settings = get_settings()
        api_key = settings.amap_api_key
    except Exception as config_error:
        logger.warning(f"无法获取高德地图API密钥: {str(config_error)}")
        import os
        api_key = os.getenv('AMAP_API_KEY', 'your_default_api_key')

    # 实现3次重试逻辑
    max_retries = 3
    retry_delay = 1  # 初始延迟1秒

    for attempt in range(max_retries):
        try:
            logger.info(f"POI搜索尝试 {attempt + 1}/{max_retries}")

            # 创建搜索工具
            search_tool = AmapPOISearchTool(api_key)

            # 执行搜索
            results = await search_tool.search_poi(
                city=city,
                keywords=keywords,
                poi_type=final_poi_type,
                page_size=min(final_limit, 20)
            )

            # 关闭会话
            await search_tool.close()

            # 检查结果是否有效
            if results and len(results) > 0:
                # 限制返回数量
                final_results = results[:final_limit]
                logger.info(f"search_poi成功返回 {len(final_results)} 个POI结果")
                return final_results
            else:
                logger.warning(f"第{attempt + 1}次尝试返回空结果")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    logger.error("所有重试都返回空结果")
                    break

        except Exception as e:
            logger.error(f"POI搜索第{attempt + 1}次尝试失败: {str(e)}")
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                logger.error(f"POI搜索连续{max_retries}次失败，使用后备数据")

    # 所有重试都失败，返回后备数据
    logger.warning("POI搜索失败，使用后备数据")
    return _get_fallback_poi_data(keywords, city, final_limit)


def _get_fallback_poi_data(keywords: str, city: str, limit: int) -> List[Dict[str, Any]]:
    """
    后备POI数据处理 - 根据重构要求，严禁使用模拟数据

    当POI搜索失败时，返回空列表而不是模拟数据，
    确保系统使用真实数据而非虚假信息。

    Args:
        keywords: 搜索关键词
        city: 城市名称
        limit: 返回数量限制

    Returns:
        空列表（严禁返回模拟数据）
    """
    logger.warning(f"POI搜索失败，无法获取真实数据: keywords={keywords}, city={city}")
    logger.warning("根据重构要求，严禁使用模拟数据，返回空列表")

    # 根据重构文档要求：严禁使用模拟数据，必须使用真实数据
    # 当API调用失败时，返回空列表而不是虚假的模拟数据
    return []


# 注册到统一工具注册表
def register_amap_tools():
    """注册高德地图工具到统一注册表"""
    from src.tools.unified_registry import unified_registry

    # 注意：不再注册search_poi，因为AmapService已经注册了
    # 这里可以注册其他辅助工具
    logger.info("高德地图POI工具模块已加载（使用AmapService的search_poi）")


# 自动注册
register_amap_tools()
