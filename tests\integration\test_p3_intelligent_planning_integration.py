"""
P3阶段集成测试：智能规划端到端验证

验证完整的P3阶段智能规划流程：
1. 真实LLM调用与智能决策
2. 时空连续性与距离感知
3. 复杂场景的综合处理
4. 预算约束与时间管理的综合应用
"""

import pytest
import asyncio
import sys
import os
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.tools.unified_registry import unified_registry
from src.agents.services.amap_service import _get_global_amap_service
from src.core.logger import get_logger

# 导入工具模块以触发注册
import src.tools.travel_planner.icp_tools

logger = get_logger("p3_integration_test")


class TestP3IntelligentPlanningIntegration:
    """P3阶段智能规划集成测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.start_location = {
            "name": "北京王府井希尔顿酒店",
            "lat": 39.91386,
            "lon": 116.41909,
            "poi_type": "hotel"
        }
        
        # 获取核心工具
        self.calculate_nearby_tool = unified_registry.get_planner_tool("calculate_nearby_pois_sorted_by_distance")
        self.schedule_tool = unified_registry.get_planner_tool("schedule_activity")
        self.llm_thinking_tool = unified_registry.get_planner_tool("generate_planning_thought")
        self.search_poi_tool = unified_registry.get_planner_tool("search_poi")
        
        assert self.calculate_nearby_tool is not None, "calculate_nearby_pois_sorted_by_distance工具未注册"
        assert self.schedule_tool is not None, "schedule_activity工具未注册"
        assert self.llm_thinking_tool is not None, "generate_planning_thought工具未注册"
        assert self.search_poi_tool is not None, "search_poi工具未注册"

    @pytest.mark.asyncio
    async def test_dynamic_location_awareness_flow(self):
        """测试动态位置感知完整流程"""
        
        print("\n🔍 测试动态位置感知完整流程")
        
        # 步骤1: 获取真实POI数据
        amap_service = _get_global_amap_service()
        
        # 搜索北京核心景点
        pois_data = []
        search_keywords = ["故宫博物院", "天坛公园", "景山公园"]
        
        for keyword in search_keywords:
            try:
                search_result = await amap_service.search_poi(
                    keywords=keyword,
                    city="北京",
                    page_size=1
                )
                if search_result:
                    poi = search_result[0]
                    poi["poi_id"] = poi.get("id")
                    poi["poi_type"] = "scenic"
                    pois_data.append(poi)
                    print(f"  获取POI: {poi['name']}")
            except Exception as e:
                print(f"  搜索POI '{keyword}' 失败: {e}")
        
        assert len(pois_data) >= 2, "至少需要2个POI进行测试"
        
        # 步骤2: 测试距离计算和排序
        nearby_pois = self.calculate_nearby_tool(self.start_location, pois_data)
        
        # 验证距离计算
        assert len(nearby_pois) == len(pois_data), "POI数量应该保持不变"
        for poi in nearby_pois:
            assert "distance_km" in poi, f"POI {poi['name']} 应该有distance_km字段"
            assert isinstance(poi["distance_km"], (int, float)), "distance_km应该是数值"
        
        # 验证按距离排序
        distances = [poi["distance_km"] for poi in nearby_pois]
        assert distances == sorted(distances), "POI应该按距离升序排序"
        
        print(f"  ✅ 距离计算和排序正确，最近POI: {nearby_pois[0]['name']} ({nearby_pois[0]['distance_km']:.1f}km)")
        
        await amap_service.close()

    @pytest.mark.asyncio
    async def test_intelligent_meal_planning(self):
        """测试智能用餐规划能力"""
        
        print("\n🍽️ 测试智能用餐规划能力")
        
        amap_service = _get_global_amap_service()
        
        # 步骤1: 设置午餐时间场景
        current_location = self.start_location
        current_time = "12:30"
        remaining_pois = []
        
        # 步骤2: 搜索餐厅（模拟POI池中没有餐厅的情况）
        print("  模拟POI池中没有餐厅，需要动态搜索...")
        
        search_result = await self.search_poi_tool(
            keywords="餐厅 北京菜",
            city="北京",
            current_location=current_location,
            page_size=3
        )
        
        assert search_result.get('success'), "餐厅搜索应该成功"
        assert len(search_result.get('pois', [])) > 0, "应该找到餐厅"
        
        restaurants = search_result['pois']
        for restaurant in restaurants:
            restaurant['poi_type'] = 'restaurant'
        
        # 步骤3: 计算距离并让AI做决策
        nearby_options = self.calculate_nearby_tool(current_location, restaurants)
        
        complete_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": current_time,
                "current_location": current_location
            },
            "nearby_poi_options": nearby_options,
            "consolidated_intent": {
                "preferences": {
                    "attractions": {"preferred_types": ["历史文化"]},
                    "budget": {"daily_budget": 500, "spent": 200, "remaining": 300}
                }
            },
            "daily_plans": {1: []},
            "structured_itinerary": {}
        }
        
        # 步骤4: 调用真实LLM进行决策
        print("  调用真实LLM进行用餐决策...")
        
        try:
            llm_decision = await self.llm_thinking_tool(complete_state, {})
            
            # 验证AI识别了用餐时间
            assert "餐" in llm_decision.get('thought', ''), "AI应该识别用餐需求"
            assert llm_decision.get('estimated_duration_minutes') == 90, "餐厅用餐时长应该是90分钟"
            
            # 如果AI选择了搜索，验证搜索参数
            action = llm_decision.get('action', {})
            if action.get('tool_name') == 'search_poi':
                assert "餐" in action.get('parameters', {}).get('keywords', ''), "搜索关键词应该包含餐厅相关词汇"
            elif action.get('tool_name') == 'select_poi_from_pool':
                selected_name = action.get('parameters', {}).get('poi_name', '')
                assert any(r['name'] == selected_name for r in restaurants), "选择的餐厅应该在可选列表中"
            
            print(f"  ✅ AI智能识别用餐时间并做出合理决策: {action.get('tool_name')}")
            
        except Exception as e:
            print(f"  ⚠️ LLM决策过程出错: {e}")
            pytest.skip("LLM服务不可用，跳过此测试")
        
        await amap_service.close()

    @pytest.mark.asyncio
    async def test_budget_constraint_integration(self):
        """测试预算约束集成场景"""
        
        print("\n💰 测试预算约束集成场景")
        
        amap_service = _get_global_amap_service()
        
        # 步骤1: 获取不同价位的POI
        pois_data = []
        search_keywords = ["故宫博物院", "景山公园", "王府井大街"]  # 高中低不同费用
        
        for keyword in search_keywords:
            try:
                search_result = await amap_service.search_poi(
                    keywords=keyword,
                    city="北京",
                    page_size=1
                )
                if search_result:
                    poi = search_result[0]
                    poi["poi_id"] = poi.get("id")
                    poi["poi_type"] = "scenic"
                    pois_data.append(poi)
            except Exception as e:
                print(f"  搜索POI '{keyword}' 失败: {e}")
        
        if len(pois_data) < 2:
            pytest.skip("无法获取足够的POI数据进行预算测试")
        
        # 步骤2: 设置低预算场景
        current_location = self.start_location
        nearby_options = self.calculate_nearby_tool(current_location, pois_data)
        
        low_budget_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "09:00",
                "current_location": current_location
            },
            "nearby_poi_options": nearby_options,
            "consolidated_intent": {
                "preferences": {
                    "attractions": {"preferred_types": ["历史文化"]},
                    "budget": {"daily_budget": 100, "spent": 80, "remaining": 20}  # 预算很紧张
                }
            },
            "daily_plans": {1: []},
            "structured_itinerary": {}
        }
        
        # 步骤3: 测试AI是否考虑预算约束
        try:
            llm_decision = await self.llm_thinking_tool(low_budget_state, {})
            
            thought = llm_decision.get('thought', '')
            
            # 验证AI考虑了预算因素
            budget_keywords = ["预算", "费用", "便宜", "免费", "价格"]
            has_budget_consideration = any(keyword in thought for keyword in budget_keywords)
            
            if has_budget_consideration:
                print(f"  ✅ AI成功考虑预算约束")
            else:
                print(f"  ⚠️ AI未明确体现预算考虑，但决策可能仍然合理")
            
            # 验证选择了合理的POI（距离近或费用低）
            action = llm_decision.get('action', {})
            if action.get('tool_name') == 'select_poi_from_pool':
                selected_name = action.get('parameters', {}).get('poi_name', '')
                print(f"  AI选择: {selected_name}")
            
        except Exception as e:
            print(f"  ⚠️ LLM决策过程出错: {e}")
            pytest.skip("LLM服务不可用，跳过此测试")
        
        await amap_service.close()

    @pytest.mark.asyncio
    async def test_time_constraint_integration(self):
        """测试时间约束集成场景"""
        
        print("\n⏰ 测试时间约束集成场景")
        
        amap_service = _get_global_amap_service()
        
        # 步骤1: 获取不同时长的POI
        try:
            search_result = await amap_service.search_poi(
                keywords="故宫博物院",
                city="北京",
                page_size=1
            )
            if not search_result:
                pytest.skip("无法获取故宫POI数据进行时间测试")
            
            gugong_poi = search_result[0]
            gugong_poi["poi_id"] = gugong_poi.get("id")
            gugong_poi["poi_type"] = "scenic"
            
        except Exception as e:
            pytest.skip(f"搜索故宫POI失败: {e}")
        
        # 步骤2: 设置接近午餐时间的场景
        current_location = self.start_location
        nearby_options = self.calculate_nearby_tool(current_location, [gugong_poi])
        
        time_constraint_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "11:30",  # 接近午餐时间
                "current_location": current_location
            },
            "nearby_poi_options": nearby_options,
            "consolidated_intent": {
                "preferences": {
                    "attractions": {"preferred_types": ["历史文化"], "must_visit": ["故宫博物院"]},
                    "budget": {"daily_budget": 500, "spent": 100, "remaining": 400}
                }
            },
            "daily_plans": {1: []},
            "structured_itinerary": {}
        }
        
        # 步骤3: 测试AI是否识别时间冲突
        try:
            llm_decision = await self.llm_thinking_tool(time_constraint_state, {})
            
            thought = llm_decision.get('thought', '')
            action = llm_decision.get('action', {})
            estimated_duration = llm_decision.get('estimated_duration_minutes', 0)
            
            print(f"  AI思考: {thought[:100]}...")
            print(f"  AI行动: {action.get('tool_name')} - {action.get('parameters')}")
            print(f"  预估时长: {estimated_duration}分钟")
            
            # 验证AI考虑了时间因素
            time_keywords = ["时间", "午餐", "11:30", "12:00", "冲突"]
            has_time_consideration = any(keyword in thought for keyword in time_keywords)
            
            if has_time_consideration:
                print(f"  ✅ AI成功识别时间约束")
            else:
                print(f"  ⚠️ AI未明确体现时间考虑")
            
            # 如果选择了故宫，验证时长是否合理（不应该安排4小时）
            if action.get('tool_name') == 'select_poi_from_pool' and '故宫' in action.get('parameters', {}).get('poi_name', ''):
                if estimated_duration <= 90:  # 缩短版本的故宫参观
                    print(f"  ✅ AI智能调整故宫参观时长为{estimated_duration}分钟")
                else:
                    print(f"  ⚠️ AI可能未充分考虑时间约束，安排了{estimated_duration}分钟")
            
        except Exception as e:
            print(f"  ⚠️ LLM决策过程出错: {e}")
            pytest.skip("LLM服务不可用，跳过此测试")
        
        await amap_service.close()

    @pytest.mark.asyncio
    async def test_end_to_end_planning_flow(self):
        """测试端到端规划流程"""
        
        print("\n🎯 测试端到端规划流程")
        
        amap_service = _get_global_amap_service()
        
        # 模拟完整的一天规划流程
        current_location = self.start_location
        current_time = "09:00"
        final_itinerary = {}
        daily_budget = 500
        spent_budget = 0
        step = 0
        max_steps = 5  # 限制步数避免无限循环
        
        print(f"  开始完整规划流程，起始位置: {current_location['name']}")
        
        while step < max_steps and current_time < "21:00":
            step += 1
            print(f"\n  第{step}步规划 (当前时间: {current_time})")
            
            try:
                # 获取POI数据（简化版，实际应该有POI池）
                search_result = await amap_service.search_poi(
                    keywords="景点 餐厅",
                    city="北京",
                    page_size=3
                )
                
                if not search_result:
                    print("    无法获取POI数据，结束规划")
                    break
                
                pois_data = search_result
                for poi in pois_data:
                    poi["poi_id"] = poi.get("id")
                    poi["poi_type"] = "scenic"
                
                # 计算距离
                nearby_options = self.calculate_nearby_tool(current_location, pois_data)
                
                # 构建状态
                complete_state = {
                    "icp_planner_state": {
                        "current_day": 1,
                        "current_time": current_time,
                        "current_location": current_location
                    },
                    "nearby_poi_options": nearby_options,
                    "consolidated_intent": {
                        "preferences": {
                            "attractions": {"preferred_types": ["历史文化"]},
                            "budget": {"daily_budget": daily_budget, "spent": spent_budget, "remaining": daily_budget - spent_budget}
                        }
                    },
                    "daily_plans": {1: final_itinerary.get(1, [])},
                    "structured_itinerary": final_itinerary
                }
                
                # LLM决策
                llm_decision = await self.llm_thinking_tool(complete_state, {})
                action = llm_decision.get('action', {})
                estimated_duration = llm_decision.get('estimated_duration_minutes', 90)
                
                print(f"    AI决策: {action.get('tool_name')}")
                
                # 处理结束规划
                if action.get('tool_name') == 'end_day_planning':
                    print("    AI决定结束当天规划")
                    break
                
                # 处理POI选择
                if action.get('tool_name') == 'select_poi_from_pool':
                    selected_name = action.get('parameters', {}).get('poi_name', '')
                    selected_poi = next((p for p in nearby_options if p['name'] == selected_name), None)
                    
                    if selected_poi:
                        # 模拟调度活动
                        schedule_result = await self.schedule_tool(
                            poi=selected_poi,
                            activity_duration_minutes=estimated_duration,
                            current_state=complete_state
                        )
                        
                        if schedule_result.get('success'):
                            current_time = schedule_result['new_current_time']
                            current_location = schedule_result['new_current_location']
                            
                            if 1 not in final_itinerary:
                                final_itinerary[1] = []
                            final_itinerary[1].append(schedule_result['activity'])
                            
                            # 模拟预算消耗
                            spent_budget += 50  # 简化的费用计算
                            
                            print(f"    成功安排: {selected_poi['name']}")
                            print(f"    时间更新到: {current_time}")
                
            except Exception as e:
                print(f"    规划步骤出错: {e}")
                break
        
        # 验证规划结果
        activities_count = len(final_itinerary.get(1, []))
        print(f"\n  ✅ 端到端规划完成，共安排 {activities_count} 个活动")
        print(f"  最终时间: {current_time}")
        print(f"  预算使用: {spent_budget}/{daily_budget}")
        
        assert activities_count > 0, "应该至少安排一个活动"
        
        await amap_service.close()


@pytest.mark.asyncio
async def test_p3_integration_suite():
    """P3阶段集成测试套件"""
    
    print("🚀 开始P3阶段智能规划集成测试...")
    
    # 创建测试实例
    test_instance = TestP3IntelligentPlanningIntegration()
    test_instance.setup_method()
    
    # 执行各项集成测试
    await test_instance.test_dynamic_location_awareness_flow()
    await test_instance.test_intelligent_meal_planning()
    await test_instance.test_budget_constraint_integration()
    await test_instance.test_time_constraint_integration()
    await test_instance.test_end_to_end_planning_flow()
    
    print("\n🎉 P3阶段智能规划集成测试全部完成！")
    print("\n验证的核心能力：")
    print("  ✅ 动态位置感知完整流程")
    print("  ✅ 智能用餐规划能力")
    print("  ✅ 预算约束集成处理")
    print("  ✅ 时间约束智能判断")
    print("  ✅ 端到端规划流程")


if __name__ == "__main__":
    asyncio.run(test_p3_integration_suite()) 