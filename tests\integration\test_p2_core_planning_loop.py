"""
P2阶段集成测试：核心规划循环重构

测试run_icp_planning函数集成P1原子工具后的效果：
1. 动态位置感知：每次循环计算当前位置到剩余POI的距离
2. 原子化调度：使用schedule_activity工具进行时空状态更新
3. 时空连续性：验证时间推进和位置更新的正确性
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# 导入要测试的组件
from src.agents.travel_planner_lg.nodes import run_icp_planning
from src.agents.travel_planner_lg.state import StandardAgentState
from src.tools.unified_registry import unified_registry

# 确保工具被正确注册
import src.tools.travel_planner.icp_tools
import src.tools.travel_planner.consolidated_tools


class TestP2CorePlanningLoop:
    """测试P2阶段重构后的核心规划循环"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建带有真实北京POI数据的测试状态
        self.test_state: StandardAgentState = {
            "messages": [{"content": "我想在北京玩两天"}],
            "task_id": "test_p2_001",
            "user_id": "1",  # 使用真实用户数据
            "original_query": "我想在北京玩两天",
            "current_phase": "planning_ready",
            "execution_mode": "automatic",
            "framework_analysis": {
                "core_intent": {
                    "destinations": ["北京"],
                    "travel_days": 2,
                    "travel_theme": ["文化"],
                    "budget_range": "中等"
                }
            },
            "preference_analysis": {
                "attraction_preferences": {
                    "preferred_types": ["历史文化"],
                    "must_visit": ["故宫", "天安门"]
                }
            },
            "consolidated_intent": {
                "destinations": ["北京"],
                "travel_days": 2,
                "preferences": {
                    "attractions": {
                        "preferred_types": ["历史文化"],
                        "must_visit": ["故宫", "天安门"]
                    }
                }
            },
            "icp_context": {
                "planning_goals": ["为北京规划2天行程"],
                "available_tools": ["search_poi", "geocode", "get_driving_route"],
                "constraints": {
                    "max_days": 2,
                    "budget_range": "中等"
                }
            }
        }

        # 真实的北京POI数据（基于P1测试中验证的数据）
        self.real_beijing_pois = [
            {
                "name": "故宫博物院",
                "poi_id": "B000A7W1R9",
                "poi_type": "scenic",
                "location": "116.397468,39.918058",
                "lat": 39.918058,
                "lon": 116.397468,
                "rating": 4.8,
                "tags": ["历史文化", "皇家建筑"]
            },
            {
                "name": "天安门广场",
                "poi_id": "B000A7M4K2",
                "poi_type": "scenic",
                "location": "116.391365,39.907766",
                "lat": 39.907766,
                "lon": 116.391365,
                "rating": 4.7,
                "tags": ["历史文化", "广场"]
            },
            {
                "name": "天坛公园",
                "poi_id": "B000A8S7G0",
                "poi_type": "scenic",
                "location": "116.407394,39.883300",
                "lat": 39.883300,
                "lon": 116.407394,
                "rating": 4.6,
                "tags": ["历史文化", "公园"]
            }
        ]

        # 北京站作为起始位置（基于P1测试验证）
        self.beijing_station = {
            "name": "北京站",
            "lat": 39.903738,
            "lon": 116.427621,
            "poi_type": "station"
        }

    @pytest.mark.asyncio
    async def test_dynamic_location_awareness(self):
        """测试动态位置感知功能"""
        
        # 创建Mock event_bus避免真实推送
        mock_event_bus = AsyncMock()
        
        # 修改测试状态以包含POI池
        self.test_state.update({
            "master_poi_pool": self.real_beijing_pois,
            "remaining_pois": self.real_beijing_pois.copy(),
            "used_poi_ids": [],
            "notification_service": mock_event_bus
        })

        # Mock核心工具函数，但使用真实的P1原子工具
        with patch('src.agents.travel_planner_lg.nodes._plan_accommodation_first') as mock_hotel, \
             patch('src.agents.travel_planner_lg.nodes._initialize_master_poi_pool') as mock_poi_init:
            
            # 设置Mock返回值
            mock_hotel.return_value = self.beijing_station
            mock_poi_init.return_value = self.real_beijing_pois
            
            # Mock LLM决策，模拟选择第一个POI
            async def mock_think_tool(current_state, context):
                nearby_options = current_state.get("nearby_poi_options", [])
                if nearby_options:
                    selected_poi = nearby_options[0]  # 选择最近的POI
                    return {
                        "thought": f"基于位置感知，选择最近的景点：{selected_poi['name']}",
                        "action": {
                            "tool_name": "select_poi_from_pool",
                            "parameters": {"poi_name": selected_poi['name']}
                        },
                        "estimated_duration_minutes": 120
                    }
                else:
                    return {
                        "thought": "没有可用的POI选项，结束规划",
                        "action": {"tool_name": "end_day_planning", "parameters": {}},
                        "estimated_duration_minutes": 0
                    }

            # Mock action工具返回选中的POI
            async def mock_action_tool(name, task_id, **kwargs):
                poi_name = kwargs.get('poi_name', '')
                for poi in self.real_beijing_pois:
                    if poi['name'] == poi_name:
                        return poi
                return None

            # 替换unified_registry中的工具
            original_think = unified_registry.get_planner_tool("generate_planning_thought")
            original_action = unified_registry.execute_action_tool
            
            unified_registry._planner_tools["generate_planning_thought"] = mock_think_tool
            unified_registry.execute_action_tool = mock_action_tool

            try:
                # 执行测试
                result = await run_icp_planning(self.test_state)
                
                # 验证结果
                assert result.get("is_completed", False), "规划应该成功完成"
                
                daily_plans = result.get("daily_plans", {})
                assert len(daily_plans) > 0, "应该生成日程计划"
                
                # 验证事件总线调用（动态位置感知应该被记录在日志中）
                mock_event_bus.notify_planning_log.assert_called()
                
                print("✅ 动态位置感知测试通过")
                
            finally:
                # 恢复原始工具
                if original_think:
                    unified_registry._planner_tools["generate_planning_thought"] = original_think
                unified_registry.execute_action_tool = original_action

    @pytest.mark.asyncio 
    async def test_atomic_scheduling_integration(self):
        """测试原子化调度工具集成"""
        
        # 创建Mock event_bus
        mock_event_bus = AsyncMock()
        
        # 设置测试状态
        self.test_state.update({
            "master_poi_pool": self.real_beijing_pois,
            "remaining_pois": self.real_beijing_pois.copy(),
            "used_poi_ids": [],
            "notification_service": mock_event_bus
        })

        # 验证P1原子工具是否正确注册
        calculate_nearby_tool = unified_registry.get_planner_tool("calculate_nearby_pois_sorted_by_distance")
        schedule_activity_tool = unified_registry.get_planner_tool("schedule_activity")
        get_travel_time_tool = unified_registry.get_planner_tool("get_travel_time_and_distance")
        
        assert calculate_nearby_tool is not None, "calculate_nearby_pois_sorted_by_distance工具应该注册"
        assert schedule_activity_tool is not None, "schedule_activity工具应该注册"
        assert get_travel_time_tool is not None, "get_travel_time_and_distance工具应该注册"
        
        print("✅ P1原子工具注册验证通过")

    @pytest.mark.asyncio
    async def test_space_time_continuity(self):
        """测试时空连续性"""
        
        # 测试calculate_nearby_pois_sorted_by_distance工具
        calculate_nearby_tool = unified_registry.get_planner_tool("calculate_nearby_pois_sorted_by_distance")
        
        # 从北京站计算到各POI的距离
        nearby_pois = calculate_nearby_tool(self.beijing_station, self.real_beijing_pois)
        
        # 验证距离计算和排序
        assert len(nearby_pois) == len(self.real_beijing_pois), "POI数量应该保持不变"
        
        # 验证每个POI都有distance_km字段
        for poi in nearby_pois:
            assert "distance_km" in poi, f"POI {poi['name']} 应该有distance_km字段"
            assert isinstance(poi["distance_km"], (int, float)), "distance_km应该是数值"
        
        # 验证按距离排序（第一个应该是最近的）
        distances = [poi["distance_km"] for poi in nearby_pois]
        assert distances == sorted(distances), "POI应该按距离升序排序"
        
        print(f"✅ 时空连续性测试通过 - 最近POI: {nearby_pois[0]['name']} ({nearby_pois[0]['distance_km']}km)")

    @pytest.mark.asyncio
    async def test_get_travel_time_integration(self):
        """测试交通时间计算工具集成"""
        
        get_travel_time_tool = unified_registry.get_planner_tool("get_travel_time_and_distance")
        
        # 测试北京站到故宫的路线计算
        origin = self.beijing_station
        destination = self.real_beijing_pois[0]  # 故宫
        
        travel_result = await get_travel_time_tool(origin, destination)
        
        # 验证返回结果格式
        assert "duration_minutes" in travel_result, "应该包含行程时间"
        assert "distance_km" in travel_result, "应该包含距离"
        assert isinstance(travel_result["duration_minutes"], int), "行程时间应该是整数"
        assert isinstance(travel_result["distance_km"], (int, float)), "距离应该是数值"
        assert travel_result["duration_minutes"] > 0, "行程时间应该大于0"
        assert travel_result["distance_km"] > 0, "距离应该大于0"
        
        print(f"✅ 交通时间计算测试通过 - {origin['name']}到{destination['name']}: {travel_result['distance_km']}km, {travel_result['duration_minutes']}分钟")


@pytest.mark.asyncio
async def test_p2_workflow_integration():
    """P2阶段工作流集成测试"""
    
    # 创建简化的测试实例
    test_instance = TestP2CorePlanningLoop()
    test_instance.setup_method()
    
    print("开始P2阶段集成测试...")
    
    # 执行各项测试
    await test_instance.test_dynamic_location_awareness()
    await test_instance.test_atomic_scheduling_integration()
    await test_instance.test_space_time_continuity()
    await test_instance.test_get_travel_time_integration()
    
    print("🎉 P2阶段所有集成测试通过！")


if __name__ == "__main__":
    asyncio.run(test_p2_workflow_integration()) 