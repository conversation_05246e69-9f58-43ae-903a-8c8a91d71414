# 智能规划生成系统优化方案

本文档旨在指导开发人员对现有的智能规划生成系统进行重构和优化，目标是提升规划的合理性、人性化和个性化程度。优化将围绕以下四个核心方向展开。

---

## 一、 上下文（`complete_state`）信息补充

为了让 LLM 能够做出更符合人类常识和旅行习惯的决策，我们需要在传递给它的核心上下文 `complete_state` 对象中，补充更丰富的“软性”信息。当前的上下文偏重于时空、预算等硬性数据，缺乏对行程节奏和状态的感知。

### 1.1. 用餐节奏与饥饿状态 (Meal Rhythm & Hunger State)

**目标**: 让 LLM 自主判断“何时该吃饭”，而不是依赖外部的硬编码时间规则。

**建议补充字段**:
在 `icp_planner_state` 或 `consolidated_intent` 中添加：
```json
"human_context": {
  "last_meal_info": {
    "type": "lunch", 
    "time": "12:30"
  },
  "time_since_last_meal": 180, 
  "meals_scheduled_today": ["lunch"] 
}
```

**开发说明**:
- `last_meal_info` 在每次安排完用餐活动后更新。
- `time_since_last_meal` 在每个规划步骤开始时，根据 `current_time` 和 `last_meal_info.time` 动态计算。
- LLM 可以利用此信息推理：“距离午餐已超过4小时，现在应优先规划晚餐。”

### 1.2. 活动强度与个人精力 (Activity Intensity & Energy Level)

**目标**: 让 LLM 理解不同活动的体力消耗，动态调整行程节奏，避免过度疲劳。

**建议补充字段**:
1.  在 **POI 数据**中增加 `intensity` 字段:
    - 例如: `{"name": "故宫博物院", "intensity": "high"}`
    - `high`: 长时间步行、消耗大的活动（如爬山、大型博物馆）。
    - `medium`: 普通强度的活动（如逛街、主题公园）。
    - `low`: 休闲放松的活动（如喝咖啡、看电影、公园散步）。

2.  在 `human_context` 中增加精力状态追踪:
```json
"human_context": {
  "cumulative_intensity_today": {
    "high": 180, 
    "medium": 60,
    "low": 30
  },
  "estimated_energy_level": "low" 
}
```

**开发说明**:
- `cumulative_intensity_today` 在每次安排完活动后，根据活动时长和其 `intensity` 属性进行累加。
- `estimated_energy_level` 可以通过一个简单的规则引擎或另一个小型LLM调用来估算。
- LLM 可以利用此信息推理：“上午已经进行了3小时高强度活动，精力水平较低，下午应安排一个低强度的活动来放松。”

---

## 二、 优化方向：分层规划 (Hierarchical Planning)

**目标**: 克服当前“线性决策”模式的短视问题，从宏观上提升一天行程的主题性和逻辑性。

**核心思想**: 引入“总规划师”和“执行者”两个不同层级的 Agent。

### 2.1. 阶段一：总规划师 (Chief Planner Agent)

- **触发时机**: 在每天规划开始时，执行一次。
- **输入**: 用户的深度画像（见第三部分）、旅行天数、城市特点。
- **核心任务**: 不选择具体POI，而是为当天或整个行程制定一个**宏观主题框架 (Theme Outline)**。
- **输出示例**:
  ```json
  {
    "day_1_theme": {
      "title": "皇家园林与胡同文化探索",
      "morning_block": {
        "theme": "皇家园林",
        "keywords": ["宫殿", "园林", "历史"],
        "time_budget": "3-4 hours"
      },
      "afternoon_block": {
        "theme": "胡同与市井文化",
        "keywords": ["胡同", "故居", "小吃", "特色小店"],
        "time_budget": "3 hours"
      },
      "evening_block": {
        "theme": "特色美食体验",
        "keywords": ["烤鸭", "老字号"],
        "time_budget": "2 hours"
      }
    }
  }
  ```

### 2.2. 阶段二：执行者 (Executor Agent)

- **触发时机**: 在“总规划师”制定完框架后，进入现有的时间驱动循环。
- **核心任务**: 作为一个“模块填充者”，在总规划师设定的框架内进行具体的POI选择和安排。
- **上下文增强**: `complete_state` 中需要加入当前正在执行的**主题模块信息**。
  ```json
  "planning_block_context": {
    "current_block": "morning_block",
    "theme": "皇家园林",
    "keywords": ["宫殿", "园林", "历史"]
  }
  ```

**开发说明**:
- “执行者”在调用`search_poi`或从POI池中筛选时，必须优先考虑符合当前`planning_block_context`主题和关键词的POI。
- 当一个模块的时间预算用尽或核心目标达成后，自动切换到下一个模块（例如，从`morning_block`切换到`afternoon_block`）。

---

## 三、 用户理解的深化：从“浅层标签”到“深度画像”

**目标**: 超越简单的“偏好”标签，构建一个能指导规划细节的、多维度的用户画像。

**核心思想**: 在`consolidated_intent`中扩展`preferences`对象，使其成为一个丰富的`user_profile`。

### 3.1. 旅行节奏 (Travel Pace)

- **字段**: `user_profile.pace`
- **值**:
  - `fast-paced`: "特种兵模式"，倾向于安排更多活动，接受较短的休息时间。
  - `moderate`: 普通节奏，劳逸结合。
  - `relaxed`: "度假模式"，活动安排松散，注重体验和休息。
- **作用**: 直接影响一天规划活动的总量和密度。

### 3.2. 详细兴趣图谱 (Detailed Interest Graph)

- **字段**: `user_profile.interests`
- **值**: 一个结构化的对象，可以包含评分或权重。
  ```json
  "interests": {
    "history": { "level": 5, "sub_tags": ["明清历史", "博物馆"] },
    "food": { "level": 5, "sub_tags": ["地道小吃", "深夜食堂", "网红餐厅"] },
    "art": { "level": 3, "sub_tags": ["现代艺术", "画廊"] },
    "photography": { "level": 4, "sub_tags": ["日出日落机位", "建筑摄影"] },
    "nature": { "level": 2, "sub_tags": ["登山", "湖泊"] }
  }
  ```
- **作用**: 为“总规划师”制定每日主题提供关键输入，也为“执行者”筛选POI提供更精细的指导。

### 3.3. 隐性偏好与限制 (Implicit Preferences & Constraints)

- **字段**: `user_profile.constraints`
- **值**:
  ```json
  "constraints": {
    "physical_limits": ["avoid_long_walks", "no_strenuous_hiking"], 
    "transport_preference": ["subway", "walking"], 
    "max_wait_time": 60, 
    "min_rating": 4.0, 
    "dietary_restrictions": ["vegetarian"] 
  }
  ```
- **作用**: 作为规划过程中的硬性约束，确保方案的可行性和安全性，提升用户满意度。

**开发说明**:
- 用户画像信息需要通过与用户的多轮对话或前端的结构化表单来收集。
- 这些画像数据应在整个规划生命周期中持续存在，并作为所有Agent决策的核心依据。

---

## 四、 核心Prompt重构 (`05_icp_step_planner.md`)

**目标**: 将所有新的上下文和规划逻辑，通过Prompt“教会”LLM，使其能够真正理解并执行我们的新策略。这是连接数据和智能决策的桥梁，是本次优化的重中之重。

### 4.1. 核心角色转变

LLM的角色必须从一个简单的“POI选择器”转变为一个“**情境感知决策者**”。Prompt需要明确其新的、更高级的职责。

### 4.2. 关键指令更新

Prompt中必须包含以下新的指令，并提供清晰的示例：

1.  **遵循分层框架**: 
    - **指令**: “你的首要任务是遵循`planning_block_context`中定义的当日主题。例如，如果当前是'上午的皇家园林'模块，你必须优先选择符合该主题的POI。不要仅仅因为某个购物中心距离更近就选择它。”

2.  **利用人性化上下文**:
    - **指令**: “在决策前，必须检查`human_context`：
        - 检查 `time_since_last_meal`。如果距离上一餐超过4小时，你的**首要目标**是规划一顿饭。
        - 检查 `estimated_energy_level`。如果精力水平为`low`，**避免**推荐`intensity`为`high`的活动，应推荐`low`强度的活动。”

3.  **应用深度用户画像**:
    - **指令**: “你的决策必须严格遵守`user_profile`中的所有细节：
        - 根据用户的`pace`（旅行节奏）来估算活动时长。
        - 确保你的推荐符合用户的详细`interests`图谱。
        - 绝对不能违反`constraints`中定义的任何硬性约束。”

### 4.3. 更新思维链与输出格式

- **重写思维链 (Chain-of-Thought) 示例**: 提供一个完整的、遵循新逻辑的决策思考过程范例，以引导LLM形成正确的思考模式。
    - **范例**: "1. **当前模块**: 下午的'胡同与市井文化'。2. **用户画像**: 节奏`moderate`，喜欢`地道小吃`。3. **身体状态**: 精力`medium`，距离午餐3小时。4. **决策**: 精力尚可，时间合适，应选择一个符合胡同文化主题的POI。5. **筛选**: `nearby_poi_options`中的'南锣鼓巷'最符合主题和用户兴趣。6. **行动**: 选择'南锣鼓巷'，并根据`moderate`节奏估算一个合理的游玩时长。"

- **同步输出格式**: 确保Prompt中定义的输出格式（如JSON）与代码中工具的输入参数完全一致。

**开发说明**:
- `05_icp_step_planner.md` 的修改需要与 `complete_state` 的最终数据结构、`user_profile` 的设计以及工具集的函数签名**严格对齐**。
- 建议在完成前三部分的代码重构后，集中精力进行该Prompt的编写与调试。