# V3.1 时空连续性修复与智能规划升级方案

本文档旨在提供一个详细、可执行的逻辑修复方案，以解决当前旅行规划中存在的时空不连续、动态应变能力不足等核心BUG。本方案遵循P1、P2、P3的增量开发策略，确保每一步的修改都可被验证。

---

## P1: 构建原子化时空工具 (Atomic Spatio-Temporal Tools)

**目标**: 在现有 `src/tools/travel_planner/icp_tools.py` 文件中，新增三个核心的时空原子工具，为上层逻辑提供稳定、可靠的基础能力。

### 1.1. 新增 `get_travel_time_and_distance` 工具
- **职责**: 计算任意两点间的驾驶时间和距离。
- **逻辑**:
    - 接收 `origin` 和 `destination` 两个地理位置对象作为输入。
    - 内部封装并调用 `tools/Amap/map_tool.py` 中已有的 `MapTool.get_route` 方法。
    - 从高德API返回的复杂路线结果中，解析并提取出核心的 `duration` (耗时，秒) 和 `distance` (距离，米)。
    - 将结果格式化为一个简单的字典，例如 `{"duration_minutes": 20, "distance_km": 5.3}`，并返回。
- **引用**: 此工具是实现 `schedule_activity` 原子化调度的前置依赖，其概念源于 **`规划合理性BUG.md`** 的 `3.2. 工具层` 部分关于 `get_travel_time_and_distance` 的设计。

### 1.2. 新增 `calculate_nearby_pois_sorted_by_distance` 工具
- **职责**: 实现动态位置感知的核心计算逻辑。
- **逻辑**:
    - 接收 `current_location` (当前位置) 和 `remaining_pois` (POI池)作为输入。
    - 遍历 `remaining_pois` 列表中的每一个POI。
    - **实时计算** `current_location` 与每个POI之间的直线地理距离（可使用Haversine公式）。
    - 将计算出的距离（公里）作为一个新字段（例如 `distance_km`）**附加**到每个POI对象上。
    - **按 `distance_km` 字段对整个列表进行升序排序**。
    - 返回这个经过计算、排序和增强的 `nearby_poi_options` 列表。
- **引用**: 这是实现 **`规划距离逻辑.md`** 中所述 **“2.2. 上下文感知的AI思考”** 核心机制的第一步，即“动态位置感知 (Pre-computation)”。

### 1.3. 新增 `schedule_activity` 工具
- **职责**: 这是驱动时空状态更新的**核心原子工具**，负责将一个已决策的活动精确地安排到日程中。
- **逻辑**:
    1. 接收一个已选定的 `poi` 对象和 `activity_duration_minutes` (活动时长)作为输入。
    2. **调用(1.1) `get_travel_time_and_distance` 工具**，获取从 `state.icp_planner_state.current_location` 到目标 `poi` 的交通耗时。
    3. 根据交通耗时和 `state.icp_planner_state.current_time`，计算出活动的精确 `start_time`。
    4. 根据 `start_time` 和 `activity_duration_minutes`，计算出活动的精确 `end_time`。
    5. 构建一个完整的活动对象，包含交通信息、起止时间、POI详情，并将其添加至 `state.structured_itinerary` 中对应天数的列表里。
    6. **以原子方式一次性更新 `state.icp_planner_state`**：
        - 推进 `current_time` (增加 `交通时间` + `活动时长`)。
        - 更新 `current_location` 为当前活动 `poi` 的位置。
- **引用**: 此工具的设计严格遵循 **`规划距离逻辑.md`** 中定义的 **“2.3. 原子化的时空工具”**，是确保时空连续性的根本保证。同时，它也实现了 **`推送.md`** 文档中 **`ITINERARY_UPDATE`** 事件的数据来源，每次调用此工具都意味着一个行程卡片可以被推送到前端（如 **`旅游搭子UI.md`** 所示）。

### 1.4. 新增 `search_poi_by_name` 工具
- **职责**: 赋予AI在规划中途搜索其初始POI池中不存在的特定地点的能力。
- **逻辑**:
    - 接收 `poi_name` 和 `city` 作为输入。
    - 作为高层封装，其内部调用 `tools/Amap/map_tool.py` 中已有的 `MapTool.search_pois` 方法来完成实际的地图API请求。
    - 将返回的POI结果进行格式化，使其符合项目中POI的数据结构。
- **引用**: 此工具的实现遵循 **`规划合理性BUG.md`** 文档中 **“3.2. 工具层”** 的要求，解决了规划中因POI池不全而卡死的BUG。

- **P1阶段测试要求**: 此阶段的开发工作必须与 **“第一层：单元测试”** 同步进行。每完成一个原子工具，都应立刻编写并通对应的单元测试，以确保基础模块的可靠性。

---

## P2: 重构核心规划循环 (Refactor the Core Planning Loop)

**目标**: 修改 `src/agents/travel_planner_lg/nodes.py` 文件中的 `run_icp_planning` 函数，将P1中创建的原子工具集成到主流程中，让时空动态规划的循环运转起来。

### 2.1. 导入与准备
- 在文件顶部，正确导入在P1中创建的新工具函数。

### 2.2. 修改 `while` 循环结构
- 在 `run_icp_planning` 函数的 `while` 循环（每日活动规划循环）的**起始位置**，**必须**添加新的逻辑步骤。
- **调用 (1.2) `calculate_nearby_pois_sorted_by_distance` 工具**，传入当前的 `current_location` 和 `remaining_pois`，生成 `nearby_poi_options` 列表。
- 将这个新生成的 `nearby_poi_options` 列表存入当前的规划状态 `current_planning_state` 中，以便传递给下一步的 `think_tool`。

### 2.3. 连接核心流程
- **Think -> Decide -> Act -> Schedule** 的新流程串联：
    1. **Think**: 调用 `generate_planning_thought` 工具（此时它还未被改造，但可以接收到含有 `nearby_poi_options` 的新状态）。
    2. **Decide & Act**: 解析LLM返回的 `action`，并执行（例如 `select_poi_from_pool`）。
    3. **Schedule**: **【核心改造点】** 在Action执行成功后，**必须调用 (1.3) `schedule_activity` 工具**。
        - **移除**旧的、手动的状态更新代码。
        - `schedule_activity` 的调用将自动完成所有的时间推进、位置更新和行程添加工作。
- **验证**: 此阶段完成后，即使AI决策不完美，整个循环也能跑通，并且 `current_time` 和 `current_location` 会在每次迭代后被精确更新。
- **引用**: 这部分修改是对 **`规划距离逻辑.md`** 中 **“3. 工作流示例”** 的直接代码实现，将理论上的时空连续性逻辑落地到实际的循环中。

---

## P3: 优化AI思考能力 (Enhance the AI's Brain)

**目标**: 改造 `generate_planning_thought` 工具（位于 `icp_tools.py`），让AI能够理解并利用我们提供的动态时空上下文。

### 3.1. 修改函数签名
- 确保 `generate_planning_thought` 函数能够从 `current_state` 中正确地读取到我们在P2中添加的 `nearby_poi_options` 列表。

### 3.2. 重构Prompt (核心)
- 这是让AI变得智能的关键。
- **格式化上下文**: 将 `nearby_poi_options` 列表（包含POI名称、类型、**距离**等信息）格式化为清晰的、对LLM友好的字符串或JSON结构。
- **注入Prompt**: 将格式化后的`nearby_poi_options`字符串明确地注入到发送给LLM的Prompt中。
- **引导决策**: Prompt需要被精心设计，明确引导LLM：
    > "你当前位于 {location}，现在是 {time}。**以下是你附近按距离从近到远排序的可用选项: {nearby_poi_options}**。请结合用户偏好，并优先考虑地理位置最近的选项，告诉我下一步最合理的行动是什么？"
- **引用**: 这是对 **`规划距离逻辑.md`** 文档核心思想 **“2.2. 上下文感知的AI思考”** 中 **“上下文注入 (Context Injection)”** 环节的具体实现，是本次重构的画龙点睛之笔。

---

## 测试策略 (Testing Strategy)

为确保本次重构的质量和稳定性，测试需要分层进行，严格遵循从单元到集成再到端到端的验证流程。

### 第一层：单元测试 (Unit Testing)
- **执行时机**: **与P1阶段同步进行**，是P1阶段完成的验收标准。
- **目标**: 在 `tests/unit/` 目录下创建 `test_icp_tools.py`，验证原子工具的可靠性。
- **关键用例**:
    - `test_calculate_nearby_pois`: 断言返回的列表是否按 `distance_km` 正确排序。
    - `test_schedule_activity`: **使用 `unittest.mock` 来模拟 `get_travel_time_and_distance`**，使其返回固定值。然后断言调用后，`planning_state` 中的 `current_time`、`current_location` 和 `structured_itinerary` 是否被正确更新。

### 第二层：集成测试
- **目标**: 在 `tests/integration/` 目录下创建 `test_icp_planning_flow.py`，验证整个流程。
- **关键用例**:
    - **使用 `unittest.mock` 来模拟 `generate_planning_thought`**，使其按顺序返回预设好的、确定的 `action` 列表（例如：先去天安门，再去故宫）。
    - 运行完整的 `run_icp_planning` 流程。
    - 断言最终生成的 `final_itinerary` 中的活动顺序、时间推进、地理位置变化是否符合逻辑，验证时空连续性。

### 第三层：手动端到端测试
- **目标**: 在真实环境中验证最终效果。
- **操作**: 运行服务，通过API或前端页面输入真实查询。
- **观察**:
    - 观察SSE推送的 `PLANNING_LOG` 和 `ITINERARY_UPDATE` 事件（如 **`推送.md`** 所定义），验证AI的思考过程和行程构建过程是否符合预期。
    - 检查最终规划的合理性。
