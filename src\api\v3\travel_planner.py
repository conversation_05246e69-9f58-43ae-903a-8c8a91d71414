"""
旅行规划API V3.0 - 统一架构版

基于统一架构的SSE流式API端点，支持：
1. 两阶段工作流（意图分析 + ICP规划）
2. 实时事件流
3. 统一状态管理
4. 错误处理和恢复
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from src.agents.travel_planner_lg.graph_v3 import TravelPlannerGraphV3
from src.services.unified_event_bus import UnifiedEventBus
from src.database.redis_client import get_redis_client

logger = logging.getLogger(__name__)

# 创建FastAPI Router
router = APIRouter(prefix="/api/v3/travel-planner", tags=["travel-planner-v3"])


class PlanRequest(BaseModel):
    """旅行规划请求模型"""
    user_query: str
    user_id: Optional[str] = None
    execution_mode: str = "automatic"
    user_profile: Optional[Dict[str, Any]] = None


@travel_planner_v3_bp.route('/plan', methods=['POST'])
@cross_origin()
def plan_travel():
    """
    旅行规划API端点 (V3.0)
    
    支持SSE流式响应和统一架构事件流
    """
    try:
        # 解析请求数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        user_query = data.get('user_query', '')
        if not user_query:
            return jsonify({"error": "user_query is required"}), 400
        
        # 准备输入数据
        input_data = {
            "user_query": user_query,
            "user_id": data.get('user_id', 'anonymous'),
            "execution_mode": data.get('execution_mode', 'automatic'),
            "user_profile": data.get('user_profile', {}),
            "task_id": f"task_{int(datetime.now().timestamp())}"
        }
        
        # 创建SSE响应
        def generate_sse_stream():
            try:
                # 创建事件总线
                redis_client = get_redis_client()
                event_bus = UnifiedEventBus(redis_client)
                
                # 创建图实例
                graph = TravelPlannerGraphV3(event_bus=event_bus)
                
                # 发送开始事件
                yield f"data: {json.dumps({'event': 'start', 'data': {'task_id': input_data['task_id']}, 'timestamp': datetime.utcnow().isoformat()}, ensure_ascii=False)}\n\n"
                
                # 执行工作流并流式返回结果
                async def run_workflow():
                    try:
                        async for step in graph.stream(input_data):
                            # 转换步骤为SSE事件
                            for node_name, node_result in step.items():
                                if isinstance(node_result, dict):
                                    # 发送节点完成事件
                                    event_data = {
                                        "event": "node_complete",
                                        "data": {
                                            "node_name": node_name,
                                            "result": node_result,
                                            "timestamp": datetime.utcnow().isoformat()
                                        }
                                    }
                                    yield f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"
                        
                        # 发送完成事件
                        yield f"data: {json.dumps({'event': 'complete', 'timestamp': datetime.utcnow().isoformat()}, ensure_ascii=False)}\n\n"
                        yield f"data: {json.dumps({'event': 'eos', 'timestamp': datetime.utcnow().isoformat()}, ensure_ascii=False)}\n\n"
                        
                    except Exception as e:
                        logger.error(f"Workflow execution failed: {str(e)}")
                        error_event = {
                            "event": "error",
                            "data": {
                                "message": str(e),
                                "error_code": "WORKFLOW_ERROR",
                                "timestamp": datetime.utcnow().isoformat()
                            }
                        }
                        yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                        yield f"data: {json.dumps({'event': 'eos', 'timestamp': datetime.utcnow().isoformat()}, ensure_ascii=False)}\n\n"
                
                # 运行异步工作流
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    async_gen = run_workflow()
                    while True:
                        try:
                            event = loop.run_until_complete(async_gen.__anext__())
                            yield event
                        except StopAsyncIteration:
                            break
                finally:
                    loop.close()
                    
            except Exception as e:
                logger.error(f"SSE stream generation failed: {str(e)}")
                error_event = {
                    "event": "error",
                    "data": {
                        "message": f"Stream generation failed: {str(e)}",
                        "error_code": "STREAM_ERROR",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }
                yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                yield f"data: {json.dumps({'event': 'eos', 'timestamp': datetime.utcnow().isoformat()}, ensure_ascii=False)}\n\n"
        
        # 返回SSE响应
        return Response(
            generate_sse_stream(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
            }
        )
        
    except Exception as e:
        logger.error(f"Travel planning API failed: {str(e)}")
        return jsonify({
            "error": "Internal server error",
            "message": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500


@travel_planner_v3_bp.route('/status/<task_id>', methods=['GET'])
@cross_origin()
def get_task_status(task_id: str):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务状态信息
    """
    try:
        # 创建事件总线
        redis_client = get_redis_client()
        event_bus = UnifiedEventBus(redis_client)
        
        # 获取任务状态
        async def get_status():
            return await event_bus.get_task_status(task_id)
        
        # 运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            status = loop.run_until_complete(get_status())
        finally:
            loop.close()
        
        if status is None:
            return jsonify({
                "error": "Task not found",
                "task_id": task_id,
                "timestamp": datetime.utcnow().isoformat()
            }), 404
        
        return jsonify({
            "task_id": task_id,
            "status": status,
            "timestamp": datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Get task status failed: {str(e)}")
        return jsonify({
            "error": "Internal server error",
            "message": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500


@travel_planner_v3_bp.route('/health', methods=['GET'])
@cross_origin()
def health_check():
    """
    健康检查端点
    
    Returns:
        服务健康状态
    """
    try:
        # 检查Redis连接
        redis_client = get_redis_client()
        
        # 检查工具注册表
        from src.tools.unified_registry import unified_registry
        tool_info = unified_registry.get_tool_info()
        
        return jsonify({
            "status": "healthy",
            "version": "3.0",
            "architecture": "unified",
            "components": {
                "redis": "connected",
                "tool_registry": tool_info,
                "event_bus": "available"
            },
            "timestamp": datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500


@travel_planner_v3_bp.route('/tools', methods=['GET'])
@cross_origin()
def get_available_tools():
    """
    获取可用工具列表
    
    Returns:
        工具注册表信息
    """
    try:
        from src.tools.unified_registry import unified_registry
        
        tool_info = unified_registry.get_tool_info()
        action_schemas = unified_registry.get_all_action_schemas()
        
        return jsonify({
            "tool_registry": tool_info,
            "action_schemas": action_schemas,
            "timestamp": datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Get tools failed: {str(e)}")
        return jsonify({
            "error": "Internal server error",
            "message": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500
