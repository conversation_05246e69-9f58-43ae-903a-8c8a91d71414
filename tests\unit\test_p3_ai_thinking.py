"""
P3阶段单元测试：AI思考能力优化验证

测试AI在以下方面的智能决策能力：
1. 距离优先选择逻辑
2. 时间段适应性决策
3. 预算约束考虑
4. 活动时长合理估算
5. 特殊情况处理
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, Mock, patch
from typing import Dict, Any

from src.tools.travel_planner.icp_tools import generate_planning_thought
from src.core.llm_manager import LLMManager


class TestP3AIThinkingCapabilities:
    """测试P3阶段AI思考能力的优化"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.mock_location = {
            "name": "天安门广场",
            "lat": 39.907766,
            "lon": 116.391365
        }
        
        # 模拟按距离排序的POI选项（P3核心功能）
        self.nearby_poi_options = [
            {
                "name": "故宫博物院",
                "type": "scenic", 
                "rating": 4.8,
                "distance_km": 0.5,  # 最近
                "business_hours": "08:30-17:00",
                "address": "北京市东城区景山前街4号",
                "display_text": "故宫博物院 (距离0.5km, 评分4.8)"
            },
            {
                "name": "景山公园",
                "type": "scenic",
                "rating": 4.5,
                "distance_km": 1.2,  # 第二近
                "business_hours": "06:30-20:00",
                "address": "北京市西城区景山西街44号",
                "display_text": "景山公园 (距离1.2km, 评分4.5)"
            },
            {
                "name": "王府井大街",
                "type": "shopping",
                "rating": 4.3,
                "distance_km": 2.8,  # 较远
                "business_hours": "10:00-22:00", 
                "address": "北京市东城区王府井大街",
                "display_text": "王府井大街 (距离2.8km, 评分4.3)"
            }
        ]
        
        # 基础状态模板
        self.base_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_location": self.mock_location,
                "current_time": "09:00"
            },
            "nearby_poi_options": self.nearby_poi_options,
            "consolidated_intent": {
                "preferences": {
                    "attractions": {
                        "preferred_types": ["历史文化"],
                        "must_visit": ["故宫博物院"]
                    },
                    "budget": {
                        "daily_budget": 500,
                        "spent": 0,
                        "remaining": 500
                    }
                }
            },
            "daily_plans": {1: []}
        }

    @pytest.mark.asyncio
    async def test_distance_priority_decision(self):
        """测试距离优先决策逻辑"""
        
        # 模拟LLM返回距离优先的决策
        mock_llm_response = {
            "content": json.dumps({
                "thought": "现在是上午09:00，我位于天安门广场。检查nearby_poi_options，最近的景点是'故宫博物院'(距离0.5km，评分4.8)，符合用户的must_visit列表，且距离最近可以节省交通时间，因此选择前往参观。",
                "action": {
                    "tool_name": "select_poi_from_pool",
                    "parameters": {"poi_name": "故宫博物院"}
                },
                "estimated_duration_minutes": 180
            }, ensure_ascii=False)
        }
        
        with patch.object(LLMManager, 'get_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat.return_value = mock_llm_response
            mock_get_client.return_value = mock_client
            
            # 执行测试
            result = await generate_planning_thought(
                current_state=self.base_state,
                planning_context={}
            )
            
            # 验证结果
            assert result["action"]["parameters"]["poi_name"] == "故宫博物院"
            assert "距离0.5km" in result["thought"], "AI应该在思考中明确提到距离信息"
            assert result["estimated_duration_minutes"] == 180, "大型景点应该估算合理的时长"
            
            print("✅ 距离优先决策测试通过")

    @pytest.mark.asyncio
    async def test_meal_time_awareness(self):
        """测试用餐时间感知能力"""
        
        # 设置午餐时间场景
        lunch_state = self.base_state.copy()
        lunch_state["icp_planner_state"]["current_time"] = "12:30"
        
        # 添加餐厅选项到附近POI
        restaurant_poi = {
            "name": "四季民福烤鸭店",
            "type": "restaurant",
            "rating": 4.6,
            "distance_km": 0.8,
            "business_hours": "11:00-22:00",
            "address": "北京市东城区东华门大街",
            "display_text": "四季民福烤鸭店 (距离0.8km, 评分4.6)"
        }
        lunch_state["nearby_poi_options"] = [restaurant_poi] + self.nearby_poi_options
        
        # 模拟LLM识别用餐时间的返回
        mock_llm_response = {
            "content": json.dumps({
                "thought": "现在是午餐时间(12:30)，我位于天安门广场。检查nearby_poi_options，最近的餐厅是'四季民福烤鸭店'(距离0.8km，评分4.6)，现在正是用餐时间，应该优先安排午餐。",
                "action": {
                    "tool_name": "select_poi_from_pool", 
                    "parameters": {"poi_name": "四季民福烤鸭店"}
                },
                "estimated_duration_minutes": 90
            }, ensure_ascii=False)
        }
        
        with patch.object(LLMManager, 'get_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat.return_value = mock_llm_response
            mock_get_client.return_value = mock_client
            
            # 执行测试
            result = await generate_planning_thought(
                current_state=lunch_state,
                planning_context={}
            )
            
            # 验证结果
            assert result["action"]["parameters"]["poi_name"] == "四季民福烤鸭店"
            assert "午餐时间" in result["thought"], "AI应该识别当前是用餐时间"
            assert result["estimated_duration_minutes"] == 90, "餐厅用餐时长应该是90分钟"
            
            print("✅ 用餐时间感知测试通过")

    @pytest.mark.asyncio
    async def test_budget_constraint_consideration(self):
        """测试预算约束考虑"""
        
        # 设置预算紧张场景
        budget_state = self.base_state.copy()
        budget_state["consolidated_intent"]["preferences"]["budget"] = {
            "daily_budget": 100,  # 预算很低
            "spent": 80,         # 已花费80
            "remaining": 20      # 剩余20
        }
        
        # 模拟LLM考虑预算的返回
        mock_llm_response = {
            "content": json.dumps({
                "thought": "现在是上午09:00，我位于天安门广场。检查预算情况：剩余预算只有20元，需要选择费用较低的景点。景山公园(距离1.2km)门票较便宜，相比故宫博物院更适合当前预算约束。",
                "action": {
                    "tool_name": "select_poi_from_pool",
                    "parameters": {"poi_name": "景山公园"}
                },
                "estimated_duration_minutes": 90
            }, ensure_ascii=False)
        }
        
        with patch.object(LLMManager, 'get_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat.return_value = mock_llm_response
            mock_get_client.return_value = mock_client
            
            # 执行测试
            result = await generate_planning_thought(
                current_state=budget_state,
                planning_context={}
            )
            
            # 验证结果
            assert result["action"]["parameters"]["poi_name"] == "景山公园"
            assert "预算" in result["thought"], "AI应该在思考中提到预算约束"
            
            print("✅ 预算约束考虑测试通过")

    @pytest.mark.asyncio
    async def test_time_conflict_detection(self):
        """测试时间冲突检测能力"""
        
        # 设置接近午餐时间的场景
        conflict_state = self.base_state.copy()
        conflict_state["icp_planner_state"]["current_time"] = "11:30"
        
        # 模拟LLM检测时间冲突的返回
        mock_llm_response = {
            "content": json.dumps({
                "thought": "现在是11:30，距离午餐时间很近。故宫博物院需要180分钟，如果现在进去会影响午餐安排。应该选择景山公园(距离1.2km)，60分钟可以逛完，12:30正好可以安排午餐。",
                "action": {
                    "tool_name": "select_poi_from_pool",
                    "parameters": {"poi_name": "景山公园"}
                },
                "estimated_duration_minutes": 60
            }, ensure_ascii=False)
        }
        
        with patch.object(LLMManager, 'get_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat.return_value = mock_llm_response
            mock_get_client.return_value = mock_client
            
            # 执行测试
            result = await generate_planning_thought(
                current_state=conflict_state,
                planning_context={}
            )
            
            # 验证结果
            assert result["action"]["parameters"]["poi_name"] == "景山公园"
            assert result["estimated_duration_minutes"] <= 90, "应该选择较短时长的活动"
            assert "午餐" in result["thought"], "AI应该考虑即将到来的用餐时间"
            
            print("✅ 时间冲突检测测试通过")

    @pytest.mark.asyncio 
    async def test_end_planning_decision(self):
        """测试结束规划决策"""
        
        # 设置深夜场景
        late_state = self.base_state.copy()
        late_state["icp_planner_state"]["current_time"] = "21:30"
        
        # 模拟LLM决定结束规划的返回
        mock_llm_response = {
            "content": json.dumps({
                "thought": "现在已经是21:30，时间很晚了。根据规划原则，深夜(21:00+)应该结束当天规划返回酒店休息。",
                "action": {
                    "tool_name": "end_day_planning",
                    "parameters": {}
                },
                "estimated_duration_minutes": 0
            }, ensure_ascii=False)
        }
        
        with patch.object(LLMManager, 'get_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat.return_value = mock_llm_response
            mock_get_client.return_value = mock_client
            
            # 执行测试
            result = await generate_planning_thought(
                current_state=late_state,
                planning_context={}
            )
            
            # 验证结果
            assert result["action"]["tool_name"] == "end_day_planning"
            assert "21:30" in result["thought"], "AI应该识别当前时间"
            
            print("✅ 结束规划决策测试通过")

    @pytest.mark.asyncio
    async def test_search_poi_when_pool_empty(self):
        """测试POI池为空时的搜索行为"""
        
        # 设置POI池为空的场景
        empty_state = self.base_state.copy()
        empty_state["nearby_poi_options"] = []
        empty_state["icp_planner_state"]["current_time"] = "12:00"
        
        # 模拟LLM决定搜索POI的返回
        mock_llm_response = {
            "content": json.dumps({
                "thought": "现在是午餐时间(12:00)，但nearby_poi_options为空，没有可选的POI。需要搜索附近的餐厅来安排午餐。",
                "action": {
                    "tool_name": "search_poi",
                    "parameters": {
                        "keywords": "餐厅",
                        "city": "北京",
                        "page_size": 5
                    }
                },
                "estimated_duration_minutes": 90
            }, ensure_ascii=False)
        }
        
        with patch.object(LLMManager, 'get_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat.return_value = mock_llm_response
            mock_get_client.return_value = mock_client
            
            # 执行测试
            result = await generate_planning_thought(
                current_state=empty_state,
                planning_context={}
            )
            
            # 验证结果
            assert result["action"]["tool_name"] == "search_poi"
            assert "餐厅" in result["action"]["parameters"]["keywords"]
            assert "为空" in result["thought"] or "没有" in result["thought"], "AI应该识别POI池为空的情况"
            
            print("✅ POI池为空搜索测试通过")


@pytest.mark.asyncio
async def test_p3_ai_thinking_suite():
    """P3阶段AI思考能力测试套件"""
    
    print("🧠 开始P3阶段AI思考能力单元测试...")
    
    # 创建测试实例
    test_instance = TestP3AIThinkingCapabilities()
    test_instance.setup_method()
    
    # 执行各项测试
    await test_instance.test_distance_priority_decision()
    await test_instance.test_meal_time_awareness() 
    await test_instance.test_budget_constraint_consideration()
    await test_instance.test_time_conflict_detection()
    await test_instance.test_end_planning_decision()
    await test_instance.test_search_poi_when_pool_empty()
    
    print("🎉 P3阶段AI思考能力单元测试全部通过！")
    print("\n验证的核心能力：")
    print("  ✅ 距离优先决策逻辑")
    print("  ✅ 时间段适应性决策")
    print("  ✅ 预算约束考虑")
    print("  ✅ 时间冲突检测")
    print("  ✅ 智能结束条件判断")
    print("  ✅ 特殊情况处理（POI池为空）")


if __name__ == "__main__":
    asyncio.run(test_p3_ai_thinking_suite()) 