"""
P1阶段原子化时空工具单元测试

使用真实数据和真实LLM调用进行测试，严格遵循开发规范要求。
不使用任何模拟、假数据或硬编码数据。
"""

import pytest
import asyncio
import logging
from typing import Dict, Any, List
from unittest.mock import patch

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加项目根目录到路径
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# 导入待测试的工具
from src.tools.travel_planner.icp_tools import (
    get_travel_time_and_distance,
    calculate_nearby_pois_sorted_by_distance,
    schedule_activity,
    search_poi_by_name,
    _advance_time
)

# 导入必要的服务
from src.agents.services.amap_service import _get_global_amap_service


class TestP1AtomicTools:
    """P1阶段原子化时空工具测试类"""
    
    @pytest.fixture
    def real_beijing_locations(self):
        """真实的北京地点数据"""
        return {
            "origin": {
                "name": "北京站",
                "lat": 39.901176,
                "lon": 116.427158
            },
            "destination": {
                "name": "天安门广场",
                "lat": 39.903738,
                "lon": 116.397827
            }
        }
    
    @pytest.fixture 
    def real_poi_pool(self):
        """真实的POI池数据（需要从高德API获取）"""
        return [
            {
                "id": "B0FFIP2XVH",
                "name": "故宫博物院",
                "location": "116.397128,39.918058",
                "address": "北京市东城区景山前街4号",
                "poi_type": "ATTRACTION",
                "rating": 4.8,
                "typecode": "110101"
            },
            {
                "id": "B000A7BD6C",
                "name": "颐和园",
                "location": "116.273511,39.999428",
                "address": "北京市海淀区新建宫门路19号",
                "poi_type": "ATTRACTION", 
                "rating": 4.7,
                "typecode": "110101"
            },
            {
                "id": "B000A8URXB",
                "name": "八达岭长城",
                "location": "116.013467,40.359677",
                "address": "北京市延庆区八达岭镇八达岭长城风景区",
                "poi_type": "ATTRACTION",
                "rating": 4.6,
                "typecode": "110101"
            }
        ]
    
    @pytest.fixture
    def sample_current_state(self, real_beijing_locations):
        """真实的当前状态数据"""
        return {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "09:00",
                "current_location": real_beijing_locations["origin"]
            },
            "structured_itinerary": {},
            "consolidated_intent": {
                "destinations": ["北京"],
                "travel_days": 2
            }
        }

    @pytest.mark.asyncio
    async def test_get_travel_time_and_distance_real_api(self, real_beijing_locations):
        """测试真实路线计算API调用"""
        logger.info("开始测试 get_travel_time_and_distance 工具")
        
        origin = real_beijing_locations["origin"]
        destination = real_beijing_locations["destination"]
        
        # 调用真实API
        result = await get_travel_time_and_distance(origin, destination)
        
        # 验证结果结构
        assert isinstance(result, dict)
        assert "duration_minutes" in result
        assert "distance_km" in result
        assert "origin" in result
        assert "destination" in result
        
        # 验证数据类型和合理性
        assert isinstance(result["duration_minutes"], int)
        assert isinstance(result["distance_km"], float)
        assert result["duration_minutes"] > 0
        assert result["distance_km"] > 0
        
        # 北京站到天安门距离应该在合理范围内（约3-5公里）
        assert 1 <= result["distance_km"] <= 10
        assert 1 <= result["duration_minutes"] <= 60
        
        logger.info(f"路线计算结果: {result}")
        logger.info("get_travel_time_and_distance 测试通过")

    def test_calculate_nearby_pois_sorted_by_distance_real_data(
        self, 
        real_beijing_locations, 
        real_poi_pool
    ):
        """测试动态位置感知算法使用真实数据"""
        logger.info("开始测试 calculate_nearby_pois_sorted_by_distance 工具")
        
        current_location = real_beijing_locations["origin"]  # 北京站
        
        # 调用算法
        nearby_pois = calculate_nearby_pois_sorted_by_distance(
            current_location, 
            real_poi_pool
        )
        
        # 验证返回结果
        assert isinstance(nearby_pois, list)
        assert len(nearby_pois) == len(real_poi_pool)
        
        # 验证每个POI都有距离信息
        for poi in nearby_pois:
            assert "distance_km" in poi
            assert isinstance(poi["distance_km"], (int, float))
            assert poi["distance_km"] >= 0
        
        # 验证距离排序正确性
        distances = [poi["distance_km"] for poi in nearby_pois]
        assert distances == sorted(distances)
        
        # 验证故宫应该是最近的（从北京站出发）
        closest_poi = nearby_pois[0]
        logger.info(f"最近的POI: {closest_poi['name']}, 距离: {closest_poi['distance_km']}km")
        
        # 记录所有POI的距离
        for poi in nearby_pois:
            logger.info(f"POI: {poi['name']}, 距离: {poi['distance_km']}km")
        
        logger.info("calculate_nearby_pois_sorted_by_distance 测试通过")

    @pytest.mark.asyncio
    async def test_schedule_activity_real_integration(
        self, 
        real_poi_pool, 
        sample_current_state
    ):
        """测试活动调度工具的真实集成"""
        logger.info("开始测试 schedule_activity 工具")
        
        # 选择故宫作为测试POI
        test_poi = real_poi_pool[0]  # 故宫博物院
        activity_duration = 180  # 3小时
        
        # 保存开始时间（在调用前）
        start_time = sample_current_state["icp_planner_state"]["current_time"]
        
        # 调用调度工具
        result = await schedule_activity(
            poi=test_poi,
            activity_duration_minutes=activity_duration,
            current_state=sample_current_state
        )
        
        # 验证调度成功
        assert isinstance(result, dict)
        assert result.get("success") is True
        assert "activity" in result
        assert "new_current_time" in result
        assert "new_current_location" in result
        assert "travel_info" in result
        
        # 验证活动信息完整性
        activity = result["activity"]
        assert activity["name"] == test_poi["name"]
        assert activity["poi_id"] == test_poi["id"]
        assert "start_time" in activity
        assert "end_time" in activity
        assert "transport_to" in activity
        assert activity["duration_minutes"] == activity_duration
        
        # 验证时间推进逻辑
        new_time = result["new_current_time"]
        logger.info(f"时间推进验证 - 开始: {start_time}, 结束: {new_time}")
        assert new_time != start_time  # 时间应该有推进
        
        # 验证位置更新
        new_location = result["new_current_location"]
        assert new_location["name"] == test_poi["name"]
        assert "lat" in new_location
        assert "lon" in new_location
        
        # 验证交通信息
        travel_info = result["travel_info"]
        assert "duration_minutes" in travel_info
        assert "distance_km" in travel_info
        assert travel_info["duration_minutes"] > 0
        
        logger.info(f"活动调度结果: {result}")
        logger.info("schedule_activity 测试通过")

    @pytest.mark.asyncio 
    async def test_search_poi_by_name_real_api(self):
        """测试按名称搜索POI的真实API调用"""
        logger.info("开始测试 search_poi_by_name 工具")
        
        # 搜索一个知名的北京景点
        poi_name = "天坛"
        city = "北京"
        
        # 调用真实搜索API
        result = await search_poi_by_name(
            poi_name=poi_name,
            city=city,
            task_id="test_task"
        )
        
        # 验证搜索结果
        if result is not None:
            assert isinstance(result, dict)
            assert "id" in result
            assert "name" in result
            assert "location" in result
            assert "address" in result
            assert "poi_type" in result
            
            # 验证搜索到的是相关的POI
            assert poi_name in result["name"] or "天坛" in result["name"]
            assert result["location"]  # 应该有位置信息
            # 地址可能不包含"北京"字样，但应该有有效的地址信息
            assert result["address"]  # 应该有地址信息
            
            logger.info(f"搜索结果: {result}")
        else:
            logger.warning(f"未找到POI: {poi_name}")
            # 即使未找到也不应该抛出异常
        
        logger.info("search_poi_by_name 测试通过")

    def test_advance_time_logic(self):
        """测试时间推进逻辑"""
        logger.info("开始测试 _advance_time 辅助函数")
        
        # 测试基本时间推进
        result1 = _advance_time("09:00", 30)
        assert result1 == "09:30"
        
        # 测试跨小时推进
        result2 = _advance_time("09:45", 30)
        assert result2 == "10:15"
        
        # 测试跨天推进
        result3 = _advance_time("23:30", 60)
        assert result3 == "00:30"
        
        # 测试大时间跨度
        result4 = _advance_time("10:00", 480)  # 8小时
        assert result4 == "18:00"
        
        logger.info("时间推进测试用例:")
        logger.info(f"09:00 + 30分钟 = {result1}")
        logger.info(f"09:45 + 30分钟 = {result2}")
        logger.info(f"23:30 + 60分钟 = {result3}")
        logger.info(f"10:00 + 480分钟 = {result4}")
        
        logger.info("_advance_time 测试通过")

    @pytest.mark.asyncio
    async def test_integration_workflow_real_scenario(
        self, 
        real_beijing_locations, 
        sample_current_state
    ):
        """测试完整的工作流集成场景"""
        logger.info("开始测试完整工作流集成")
        
        # 步骤1: 搜索真实POI
        poi_result = await search_poi_by_name("故宫", "北京")
        if poi_result is None:
            logger.warning("搜索POI失败，跳过集成测试")
            return
            
        # 步骤2: 模拟POI池
        poi_pool = [poi_result]
        
        # 步骤3: 计算附近POI
        current_location = real_beijing_locations["origin"]
        nearby_pois = calculate_nearby_pois_sorted_by_distance(
            current_location, 
            poi_pool
        )
        
        assert len(nearby_pois) > 0
        selected_poi = nearby_pois[0]
        
        # 步骤4: 调度活动
        schedule_result = await schedule_activity(
            poi=selected_poi,
            activity_duration_minutes=120,
            current_state=sample_current_state
        )
        
        assert schedule_result.get("success") is True
        
        # 步骤5: 验证状态更新
        new_location = schedule_result["new_current_location"]
        new_time = schedule_result["new_current_time"]
        
        # 步骤6: 基于新位置计算下一个POI（如果有更多POI的话）
        remaining_pois = [poi for poi in poi_pool if poi["id"] != selected_poi["id"]]
        if remaining_pois:
            next_nearby_pois = calculate_nearby_pois_sorted_by_distance(
                new_location, 
                remaining_pois
            )
            logger.info(f"从新位置 {new_location['name']} 计算的下一批POI: {len(next_nearby_pois)}")
        
        logger.info(f"完整工作流测试完成:")
        logger.info(f"- 搜索到POI: {poi_result['name']}")
        logger.info(f"- 计算距离: {selected_poi.get('distance_km', 'N/A')}km")
        logger.info(f"- 调度成功: {schedule_result['success']}")
        logger.info(f"- 时间推进: 09:00 -> {new_time}")
        logger.info(f"- 位置更新: {current_location['name']} -> {new_location['name']}")
        
        logger.info("完整工作流集成测试通过")


# 运行测试的主函数
if __name__ == "__main__":
    async def run_tests():
        """运行所有测试"""
        test_instance = TestP1AtomicTools()
        
        # 准备测试数据
        real_locations = {
            "origin": {"name": "北京站", "lat": 39.901176, "lon": 116.427158},
            "destination": {"name": "天安门广场", "lat": 39.903738, "lon": 116.397827}
        }
        
        real_pois = [
            {
                "id": "B0FFIP2XVH",
                "name": "故宫博物院", 
                "location": "116.397128,39.918058",
                "address": "北京市东城区景山前街4号",
                "poi_type": "ATTRACTION",
                "rating": 4.8
            }
        ]
        
        sample_state = {
            "icp_planner_state": {
                "current_day": 1,
                "current_time": "09:00", 
                "current_location": real_locations["origin"]
            },
            "structured_itinerary": {}
        }
        
        try:
            # 运行各个测试
            logger.info("=== P1阶段原子化工具单元测试开始 ===")
            
            await test_instance.test_get_travel_time_and_distance_real_api(real_locations)
            test_instance.test_calculate_nearby_pois_sorted_by_distance_real_data(real_locations, real_pois)
            await test_instance.test_schedule_activity_real_integration(real_pois, sample_state)
            await test_instance.test_search_poi_by_name_real_api()
            test_instance.test_advance_time_logic()
            await test_instance.test_integration_workflow_real_scenario(real_locations, sample_state)
            
            logger.info("=== P1阶段所有单元测试通过 ===")
            
        except Exception as e:
            logger.error(f"测试失败: {str(e)}")
            raise
    
    # 运行异步测试
    asyncio.run(run_tests()) 